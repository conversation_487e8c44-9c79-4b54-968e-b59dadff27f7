import { memo, useEffect, useRef, useState } from "react";

import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { formatPrice, formatUnixTimestamp } from "@/utils/format";
import { Virtuoso } from "react-virtuoso";
import { useMediaQuery } from "react-responsive";
import RecentTradesHandler, {
  TTradeRecent,
} from "./services/RecentTradesHandler";
import { PairSetting } from "@/types/pair";
import AppNumber from "../AppNumber";
import {
  getAggTradeRoomName,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";

export const TableMarketTrades = memo(
  ({ pairSetting }: { pairSetting: PairSetting | null }) => {
    const tradesRef = useRef<TTradeRecent[]>([]);
    const [forceRender, setForceRender] = useState(0);

    const handlerRef = useRef<RecentTradesHandler | null>(null);
    const frameIdRef = useRef<number | null>(null);
    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const socketConnected = useSelector(
      (state: RootState) => state.metadata.socketConnected
    );

    useEffect(() => {
      if (!pairSetting?.symbol) return;

      const onUpdateTrade = (updatedTrades: TTradeRecent[]) => {
        tradesRef.current = updatedTrades;

        if (frameIdRef.current !== null) {
          cancelAnimationFrame(frameIdRef.current);
        }

        frameIdRef.current = requestAnimationFrame(() => {
          setForceRender((prev) => prev + 1);
          frameIdRef.current = null;
        });
      };

      handlerRef.current = new RecentTradesHandler(
        pairSetting?.symbol,
        onUpdateTrade
      );
      handlerRef.current.initTrades();

      const handleTradeEvent = (data: TBroadcastEvent) => {
        if (handlerRef.current && data.detail) {
          handlerRef.current.formatAndUpdateTradeEvent(JSON.parse(data.detail));
        }
      };

      AppBroadcast.on(BROADCAST_EVENTS.TRADE_UPDATE, handleTradeEvent);

      return () => {
        if (frameIdRef.current !== null) {
          cancelAnimationFrame(frameIdRef.current);
        }
        AppBroadcast.remove(BROADCAST_EVENTS.TRADE_UPDATE, handleTradeEvent);
        handlerRef.current = null;
      };
    }, [pairSetting?.symbol]);

    useEffect(() => {
      if (!pairSetting?.symbol) return;

      subscribeSocketChannel({
        params: [getAggTradeRoomName(pairSetting.symbol)],
      });

      return () => {
        unsubscribeSocketChannel({
          params: [getAggTradeRoomName(pairSetting.symbol)],
        });
      };
    }, [pairSetting?.symbol, socketConnected]);

    return (
      <div className="market-trades-container">
        <div className="grid grid-cols-3">
          <div className="body-sm-regular-12 text-white-500 p-2">
            Price ({pairSetting?.quoteAsset?.toUpperCase()})
          </div>
          <div className="body-sm-regular-12 text-white-500 p-2 text-right">
            Amount ({pairSetting?.baseAsset?.toUpperCase()})
          </div>
          <div className="body-sm-regular-12 text-white-500 p-2 text-right">
            Time
          </div>
        </div>

        <Virtuoso
          className="customer-scroll"
          style={{ height: isMobile ? 400 : 300 }}
          data={tradesRef.current}
          key={`trades-virtuoso-${pairSetting?.symbol}`}
          itemContent={(index, item) => (
            <div
              key={`trade-${item.id}-${index}`}
              className="hover:bg-white-50 grid cursor-pointer grid-cols-3"
            >
              <div
                className="body-sm-regular-12 px-2 py-1"
                style={{
                  color: item.isBuyerMaker
                    ? "var(--color-red-400)"
                    : "var(--color-green-500)",
                }}
              >
                <AppNumber
                  value={item.price}
                  decimals={pairSetting?.pricePrecision}
                  isFormatLargeNumber={false}
                  className="!font-rotobo-mono"
                />
              </div>
              <div className="body-sm-regular-12 px-2 py-1 text-right">
                <AppNumber
                  value={item.quantity}
                  decimals={pairSetting?.quantityPrecision}
                  className="!font-rotobo-mono"
                />
              </div>
              <div className="body-sm-regular-12 !font-rotobo-mono px-2 py-1 text-right">
                {formatUnixTimestamp(item.time, "HH:mm:ss")}
              </div>
            </div>
          )}
        />
      </div>
    );
  }
);

TableMarketTrades.displayName = "TableMarketTrades";
