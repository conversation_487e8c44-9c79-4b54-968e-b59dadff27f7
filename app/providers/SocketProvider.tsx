"use client";
import { ReactNode, useEffect } from "react";
import { setSocketConnected } from "@/store/metadata.store";
import { updateBalance } from "@/store/account.store";
import {
  closeSocketInstance,
  createSocketInstance,
  SOCKET_NETWORK,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { RootState } from "@/store";

interface SocketProviderProps {
  children: ReactNode;
  authorization?: string;
}

export const SocketProvider = ({
  children,
  authorization,
}: SocketProviderProps) => {
  const dispatch = useAppDispatch();
  const socketConnected = useAppSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const handleWhenSocketConnected = () => {
    dispatch(setSocketConnected({ socketConnected: true }));

    if (authorization) {
      subscribeSocketChannel({
        params: [""],
        authorization,
      });
    }
  };

  const handleSocketDisconnected = () => {
    dispatch(setSocketConnected({ socketConnected: false }));
  };

  const handleBalanceUpdate = (event: any) => {
    const balanceUpdated = JSON.parse(event.detail);
    dispatch(updateBalance(balanceUpdated));
  };

  useEffect(() => {
    createSocketInstance({ network: SOCKET_NETWORK.VDAX });

    AppBroadcast.on(
      BROADCAST_EVENTS.SOCKET_CONNECTED,
      handleWhenSocketConnected
    );
    AppBroadcast.on(
      BROADCAST_EVENTS.SOCKET_DISCONNECTED,
      handleSocketDisconnected
    );
    AppBroadcast.on(BROADCAST_EVENTS.BALANCE_UPDATED, handleBalanceUpdate);

    return () => {
      closeSocketInstance(SOCKET_NETWORK.VDAX);
      AppBroadcast.remove(
        BROADCAST_EVENTS.SOCKET_CONNECTED,
        handleWhenSocketConnected
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.SOCKET_DISCONNECTED,
        handleSocketDisconnected
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.BALANCE_UPDATED,
        handleBalanceUpdate
      );
    };
  }, []);

  useEffect(() => {
    if (authorization) {
      handleWhenSocketConnected();
    }

    return () => {
      console.log("unsubcribe this shit ");
      unsubscribeSocketChannel({
        params: [""],
      });
    };
  }, [authorization, socketConnected]);

  return <>{children}</>;
};
